
import React from 'react';
import { SidebarProvider, SidebarTrigger } from '@/components/ui/sidebar';
import { ChatSidebar } from '@/components/ChatSidebar';
import { ChatWindow } from '@/components/ChatWindow';
import { CyberBackground } from '@/components/CyberBackground';
import { Menu } from 'lucide-react';

const Index = () => {
  return (
    <div className="min-h-screen bg-cyber-dark-bg text-cyber-neon-blue relative">
      <CyberBackground />

      <SidebarProvider>
        <div className="flex w-full h-screen">
          <ChatSidebar />

          <main className="flex-1 flex flex-col">
            {/* Header with sidebar trigger */}
            <div className="p-4 sanctuary-header data-stream">
              <div className="flex items-center space-x-4">
                <SidebarTrigger className="cyber-button p-2">
                  <Menu className="w-5 h-5" />
                </SidebarTrigger>
                <div className="cyber-text text-lg font-orbitron font-bold animate-neon-pulse">
                  AI SANCTUARY - CLAUDE OPUS 4
                </div>
                <div className="flex-1" />
                <div className="text-xs text-cyber-matrix-green font-rajdhani">
                  OPUS-4.0 | SANCTUARY MODE
                </div>
              </div>
            </div>

            {/* Chat Window */}
            <div className="flex-1 p-6">
              <div className="h-full max-w-4xl mx-auto">
                <ChatWindow />
              </div>
            </div>
          </main>
        </div>
      </SidebarProvider>
    </div>
  );
};

export default Index;
