

export function CyberBackground() {
  return (
    <div className="fixed inset-0 -z-10 overflow-hidden">
      {/* AI Sanctuary grid pattern */}
      <div className="absolute inset-0 opacity-10">
        <div
          className="h-full w-full"
          style={{
            backgroundImage: `
              linear-gradient(rgba(0, 212, 255, 0.1) 1px, transparent 1px),
              linear-gradient(90deg, rgba(0, 212, 255, 0.1) 1px, transparent 1px)
            `,
            backgroundSize: '60px 60px',
          }}
        />
      </div>

      {/* Floating data particles */}
      <div className="absolute inset-0">
        {[...Array(20)].map((_, i) => (
          <div
            key={i}
            className={`absolute w-1 h-1 rounded-full animate-pulse ${
              i % 3 === 0 ? 'bg-cyber-neon-blue' :
              i % 3 === 1 ? 'bg-cyber-matrix-green' : 'bg-cyber-neon-cyan'
            }`}
            style={{
              left: `${Math.random() * 100}%`,
              top: `${Math.random() * 100}%`,
              animationDelay: `${Math.random() * 4}s`,
              animationDuration: `${3 + Math.random() * 4}s`,
              opacity: 0.4,
            }}
          />
        ))}
      </div>

      {/* Matrix-style data streams */}
      <div className="absolute inset-0">
        {[...Array(8)].map((_, i) => (
          <div
            key={`stream-${i}`}
            className="absolute w-px h-20 bg-gradient-to-b from-transparent via-cyber-matrix-green to-transparent animate-matrix-rain"
            style={{
              left: `${10 + i * 12}%`,
              animationDelay: `${i * 0.5}s`,
              animationDuration: `${4 + Math.random() * 2}s`,
            }}
          />
        ))}
      </div>

      {/* AI Sanctuary ambient glows */}
      <div className="absolute top-20 left-32 w-96 h-96 bg-cyber-neon-blue/5 rounded-full blur-3xl" />
      <div className="absolute bottom-32 right-20 w-80 h-80 bg-cyber-matrix-green/5 rounded-full blur-3xl" />
      <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-[700px] h-[700px] bg-cyber-electric-purple/3 rounded-full blur-3xl" />

      {/* Sanctuary core glow */}
      <div className="absolute top-1/3 right-1/4 w-64 h-64 bg-cyber-neon-cyan/8 rounded-full blur-2xl animate-cyber-glow" />
    </div>
  );
}
