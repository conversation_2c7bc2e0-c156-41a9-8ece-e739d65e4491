import React, { useState, useRef, useEffect } from 'react';
import { Send, User, Bot, Cpu } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Message, ChatSession, saveChatSession, getCurrentSession, createNewSession, generateSessionTitle } from '@/utils/chatStorage';
import { sendMessageToAnthropic, getStoredApiKey, getStoredModel, AnthropicApiError } from '@/utils/anthropicApi';
import { ApiSettings } from './ApiSettings';
import chatAvatar from '/public/eye-of-kai_logo.png';

const welcomeMessage: Message = {
  id: 'welcome',
  content: 'Welcome to the neural network, operative. Configure your Anthropic API key in settings to begin chatting with Claude models.',
  sender: 'ai',
  timestamp: new Date(),
};

export function ChatWindow() {
  const [currentSession, setCurrentSession] = useState<ChatSession>(() => {
    const existing = getCurrentSession();
    if (existing && existing.messages.length > 0) {
      return existing;
    }
    
    const newSession = createNewSession();
    newSession.messages = [welcomeMessage];
    return newSession;
  });
  
  const [inputValue, setInputValue] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const messagesEndRef = useRef<HTMLDivElement>(null);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  useEffect(() => {
    scrollToBottom();
  }, [currentSession.messages]);

  useEffect(() => {
    if (currentSession.messages.length > 0) {
      const updatedSession = {
        ...currentSession,
        lastActivity: new Date(),
        title: currentSession.messages.length <= 1 ? 'New Neural Session' : generateSessionTitle(currentSession.messages)
      };
      saveChatSession(updatedSession);
      setCurrentSession(updatedSession);
    }
  }, [currentSession.messages]);

  const handleSendMessage = async () => {
    if (!inputValue.trim() || isLoading) return;

    const apiKey = getStoredApiKey();
    const model = getStoredModel();

    if (!apiKey) {
      const errorMessage: Message = {
        id: Date.now().toString(),
        content: 'Please configure your Anthropic API key in the settings before sending messages.',
        sender: 'ai',
        timestamp: new Date(),
      };
      
      setCurrentSession(prev => ({
        ...prev,
        messages: [...prev.messages, errorMessage]
      }));
      return;
    }

    const newMessage: Message = {
      id: Date.now().toString(),
      content: inputValue,
      sender: 'user',
      timestamp: new Date(),
    };

    const updatedMessages = [...currentSession.messages, newMessage];
    setCurrentSession(prev => ({
      ...prev,
      messages: updatedMessages
    }));
    
    setInputValue('');
    setIsLoading(true);

    try {
      const conversationHistory = updatedMessages
        .filter(m => m.id !== 'welcome')
        .map(m => ({
          role: m.sender === 'user' ? 'user' as const : 'assistant' as const,
          content: m.content
        }));

      const response = await sendMessageToAnthropic(conversationHistory, apiKey, model);
      
      const aiResponse: Message = {
        id: (Date.now() + 1).toString(),
        content: response,
        sender: 'ai',
        timestamp: new Date(),
      };
      
      setCurrentSession(prev => ({
        ...prev,
        messages: [...prev.messages, aiResponse]
      }));
    } catch (error) {
      console.error('Error sending message to Anthropic:', error);
      
      let errorContent = 'Neural connection error. Please try again.';
      if (error instanceof AnthropicApiError) {
        errorContent = `API Error: ${error.message}`;
      }
      
      const errorResponse: Message = {
        id: (Date.now() + 1).toString(),
        content: errorContent,
        sender: 'ai',
        timestamp: new Date(),
      };
      
      setCurrentSession(prev => ({
        ...prev,
        messages: [...prev.messages, errorResponse]
      }));
    } finally {
      setIsLoading(false);
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  return (
    <div className="flex flex-col h-full glass-panel rounded-lg border-2 border-cyber-neon-blue/30">
      {/* Header */}
      <div className="p-4 border-b border-cyber-glass-border">
        <div className="flex items-center space-x-2">
          <Cpu className="w-6 h-6 text-cyber-neon-blue animate-neon-pulse" />
          <h1 className="cyber-text text-xl font-bold">AI Sanctuary</h1>
          <div className="flex-1" />
          <ApiSettings 
            isOpen={false} 
            onOpenChange={(isOpen) => console.log('ApiSettings toggled:', isOpen)} 
          />
          <div className="w-2 h-2 bg-cyber-matrix-green rounded-full animate-pulse" />
          <span className="text-cyber-matrix-green text-sm font-rajdhani">ONLINE</span>
        </div>
      </div>

      {/* Messages */}
      <div className="flex-1 overflow-y-auto p-4 space-y-4">
        {currentSession.messages.map((message) => (
          <div
            key={message.id}
            className={`flex ${message.sender === 'user' ? 'justify-end' : 'justify-start'}`}
          >
            <div className={`flex items-start space-x-2 max-w-2xl ${
              message.sender === 'user' ? 'flex-row-reverse space-x-reverse' : ''
            }`}>
              <div className={`w-8 h-8 rounded-full flex items-center justify-center ${
                message.sender === 'user' 
                  ? 'bg-cyber-hot-pink/20 border border-cyber-hot-pink/50' 
                  : 'bg-cyber-neon-blue/20 border border-cyber-neon-blue/50'
              }`}>
                {message.sender === 'user' ? (
                  <User className="w-4 h-4 text-cyber-hot-pink" />
                ) : (
                  <img src={chatAvatar} alt="Chat Avatar" className="w-8 h-8 rounded-full" />
                )}
              </div>
              
              <div className={`message-bubble ${
                message.sender === 'user'
                  ? 'bg-cyber-hot-pink/10 border-cyber-hot-pink/30'
                  : 'bg-cyber-neon-blue/10 border-cyber-neon-blue/30'
              }`}>
                <p className={`font-rajdhani whitespace-pre-wrap font-bold ${
                  message.sender === 'user' ? 'text-cyber-hot-pink' : 'text-cyber-neon-blue'
                }`}>
                  {message.content}
                </p>
                <span className="text-xs text-gray-400 mt-2 block font-rajdhani">
                  {message.timestamp.toLocaleTimeString()}
                </span>
              </div>
            </div>
          </div>
        ))}
        
        {isLoading && (
          <div className="flex justify-start">
            <div className="flex items-start space-x-2 max-w-2xl">
              <div className="w-8 h-8 rounded-full flex items-center justify-center bg-cyber-neon-blue/20 border border-cyber-neon-blue/50">
                <Bot className="w-4 h-4 text-cyber-neon-blue" />
              </div>
              <div className="message-bubble bg-cyber-neon-blue/10 border-cyber-neon-blue/30">
                <p className="font-rajdhani text-cyber-neon-blue">
                  <span className="animate-pulse">Processing neural pathways...</span>
                </p>
              </div>
            </div>
          </div>
        )}
        
        <div ref={messagesEndRef} />
      </div>

      {/* Input */}
      <div className="p-4 border-t border-cyber-glass-border bg-transparent">
        <div className="flex space-x-2">
          <Input
            value={inputValue}
            onChange={(e) => setInputValue(e.target.value)}
            onKeyPress={handleKeyPress}
            placeholder="Enter your neural command..."
            className="cyber-input flex-1 font-rajdhani font-medium bg-transparent border-0 text-cyber-neon-blue placeholder:text-cyber-neon-blue/50"
            disabled={isLoading}
          />
          <Button
            onClick={handleSendMessage}
            className="cyber-button px-4 bg-transparent"
            disabled={!inputValue.trim() || isLoading}
          >
            <Send className="w-4 h-4" />
          </Button>
        </div>
      </div>
    </div>
  );
}
