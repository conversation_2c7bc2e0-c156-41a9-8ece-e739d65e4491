import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON>, DialogContent, Di<PERSON>Header, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Settings, Eye, EyeOff } from 'lucide-react';

const ANTHROPIC_MODELS = [
	{ id: 'claude-3-5-sonnet-20241022', name: '<PERSON> Sonnet 3.5' },
	{ id: 'claude-3-7-sonnet-20241201', name: '<PERSON> Sonnet 3.7' },
	{ id: 'claude-4-sonnet-20241201', name: '<PERSON> Sonnet 4' },
	{ id: 'claude-4-opus-20241201', name: '<PERSON> 4' },
	{ id: 'claude-3-5-haiku-20241022', name: '<PERSON>' },
];

interface ApiSettingsProps {
	isOpen: boolean;
	onOpenChange: React.Dispatch<React.SetStateAction<boolean>>;
}

export function ApiSettings({ isOpen, onOpenChange }: ApiSettingsProps) {
	const [apiKey, setApiKey] = useState('');
	const [selectedModel, setSelectedModel] = useState(ANTHROPIC_MODELS[0].id);
	const [showApiKey, setShowApiKey] = useState(false);

	useEffect(() => {
		const storedApiKey = localStorage.getItem('anthropic_api_key');
		const storedModel = localStorage.getItem('anthropic_model');

		if (storedApiKey) setApiKey(storedApiKey);
		if (storedModel) setSelectedModel(storedModel);
	}, []);

	const handleSave = () => {
		localStorage.setItem('anthropic_api_key', apiKey);
		localStorage.setItem('anthropic_model', selectedModel);
		onOpenChange(false);
	};

	const handleClear = () => {
		localStorage.removeItem('anthropic_api_key');
		localStorage.removeItem('anthropic_model');
		setApiKey('');
		setSelectedModel(ANTHROPIC_MODELS[0].id);
	};

	return (
		<Dialog open={isOpen} onOpenChange={onOpenChange}>
			<DialogTrigger asChild>
				<Button className="cyber-button bg-transparent">
					<Settings className="w-4 h-4" />
					AI Settings
				</Button>
			</DialogTrigger>
			<DialogContent className="glass-panel border-2 border-cyber-neon-blue/30 bg-cyber-glass-bg">
				<DialogHeader>
					<DialogTitle className="cyber-text text-cyber-neon-blue">
						Anthropic API Configuration
					</DialogTitle>
				</DialogHeader>

				<div className="space-y-4">
					<div className="space-y-2">
						<Label
							htmlFor="apiKey"
							className="cyber-text text-cyber-neon-blue"
						>
							Anthropic API Key
						</Label>
						<div className="relative">
							<Input
								id="apiKey"
								type={showApiKey ? 'text' : 'password'}
								value={apiKey}
								onChange={(e) => setApiKey(e.target.value)}
								placeholder="sk-ant-..."
								className="cyber-input bg-transparent border-cyber-neon-blue/30 text-cyber-neon-blue pr-10"
							/>
							<Button
								type="button"
								variant="ghost"
								size="sm"
								className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
								onClick={() => setShowApiKey(!showApiKey)}
							>
								{showApiKey ? (
									<EyeOff className="h-4 w-4 text-cyber-neon-blue" />
								) : (
									<Eye className="h-4 w-4 text-cyber-neon-blue" />
								)}
							</Button>
						</div>
					</div>

					<div className="space-y-2">
						<Label
							htmlFor="model"
							className="cyber-text text-cyber-neon-blue"
						>
							Claude Model
						</Label>
						<Select
							value={selectedModel}
							onValueChange={setSelectedModel}
						>
							<SelectTrigger className="cyber-input bg-transparent border-cyber-neon-blue/30 text-cyber-neon-blue">
								<SelectValue />
							</SelectTrigger>
							<SelectContent className="glass-panel border-cyber-neon-blue/30 bg-cyber-glass-bg">
								{ANTHROPIC_MODELS.map((model) => (
									<SelectItem
										key={model.id}
										value={model.id}
										className="text-cyber-neon-blue hover:bg-cyber-neon-blue/10"
									>
										{model.name}
									</SelectItem>
								))}
							</SelectContent>
						</Select>
					</div>

					<div className="flex justify-between pt-4">
						<Button
							onClick={handleClear}
							variant="outline"
							className="cyber-button bg-transparent border-cyber-hot-pink/50 text-cyber-hot-pink hover:bg-cyber-hot-pink/10"
						>
							Clear
						</Button>
						<Button
							onClick={handleSave}
							className="cyber-button bg-cyber-neon-blue/20 text-cyber-neon-blue hover:bg-cyber-neon-blue/30"
						>
							Save Configuration
						</Button>
					</div>
				</div>
			</DialogContent>
		</Dialog>
	);
}
