@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    /* Reapplying charcoal black and grey theme */
    --background: 220 15% 15%; /* Charcoal black */
    --foreground: 220 10% 85%; /* Light grey */

    --card: 220 18% 18%; /* Slightly lighter charcoal */
    --card-foreground: 220 10% 85%;

    --popover: 220 18% 18%;
    --popover-foreground: 220 10% 85%;

    --primary: 230 12% 50%; /* Muted blue-grey */
    --primary-foreground: 220 10% 90%;

    --secondary: 230 15% 30%; /* Dark grey */
    --secondary-foreground: 220 10% 85%;

    --muted: 230 12% 25%; /* Muted dark grey */
    --muted-foreground: 220 8% 65%;

    --accent: 250 22% 35%; /* Subtle accent grey */
    --accent-foreground: 220 10% 90%;

    --destructive: 0 55% 35%; /* Muted red */
    --destructive-foreground: 0 0% 95%;

    --border: 220 20% 20%; /* Grey border */
    --input: 220 20% 20%;
    --ring: 250 22% 40%; /* Subtle ring */

    --radius: 0.75rem;
  }

  .light {
    --background: 0 0% 100%;
    --foreground: 240 10% 3.9%;
    
    --card: 0 0% 100%;
    --card-foreground: 240 10% 3.9%;
    
    --popover: 0 0% 100%;
    --popover-foreground: 240 10% 3.9%;
    
    --primary: 240 5.9% 10%;
    --primary-foreground: 0 0% 98%;
    
    --secondary: 240 4.8% 95.9%;
    --secondary-foreground: 240 5.9% 10%;
    
    --muted: 240 4.8% 95.9%;
    --muted-foreground: 240 3.8% 46.1%;
    
    --accent: 240 4.8% 95.9%;
    --accent-foreground: 240 5.9% 10%;
    
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 98%;

    --border: 240 5.9% 90%;
    --input: 240 5.9% 90%;
    --ring: 240 5.9% 10%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground;
    font-family: system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
  }
}

@layer components {
  .message-container {
    @apply rounded-lg p-4 transition-all duration-200 ease-in-out;
    background: hsl(var(--background));
    border: 1px solid hsl(var(--border));
  }

  .message-input {
    @apply rounded-lg px-4 py-3 w-full transition-all duration-200;
    background: hsl(var(--background));
    border: 1px solid hsl(var(--border));
  }

  .message-input:focus {
    @apply outline-none ring-2;
    border-color: hsl(var(--ring));
    box-shadow: 0 0 0 2px hsl(var(--ring) / 0.1);
  }

  /* Chat message text styling */
  .message-bubble p {
    @apply text-lg font-medium;
    text-shadow: 0 0 8px currentColor;
  }

  /* Input text styling */
  .cyber-input {
    @apply text-lg font-medium;
  }
  
  /* Sidebar text styling */
  .cyber-text {
    text-shadow: 0 0 8px currentColor;
  }
  
  .font-orbitron {
    text-shadow: 0 0 6px currentColor;
  }
  
  /* Make sidebar menu items more visible */
  .cyber-button span {
    text-shadow: 0 0 4px currentColor;
  }
}
