import { getOrCreateDeviceId } from './deviceId';

export interface Message {
  id: string;
  content: string;
  sender: 'user' | 'ai';
  timestamp: Date;
}

export interface ChatSession {
  id: string;
  title: string;
  messages: Message[];
  lastActivity: Date;
}

const CHAT_STORAGE_KEY = 'cyber_chat_sessions';

export function saveChatSession(session: ChatSession): void {
  const deviceId = getOrCreateDeviceId();
  const storageKey = `${CHAT_STORAGE_KEY}_${deviceId}`;

  try {
    const existingSessions = getChatSessions();
    const sessionIndex = existingSessions.findIndex(s => s.id === session.id);

    if (sessionIndex >= 0) {
      existingSessions[sessionIndex] = session;
    } else {
      existingSessions.unshift(session);
    }

    // Keep only the last 20 sessions to avoid storage bloat
    const limitedSessions = existingSessions.slice(0, 20);

    localStorage.setItem(storageKey, JSON.stringify(limitedSessions));
  } catch (error) {
    console.error('Failed to save chat session:', error);
  }
}

export function getChatSessions(): ChatSession[] {
  const deviceId = getOrCreateDeviceId();
  const storageKey = `${CHAT_STORAGE_KEY}_${deviceId}`;

  try {
    const stored = localStorage.getItem(storageKey);
    if (!stored) return [];

    const sessions = JSON.parse(stored);

    // Convert timestamp strings back to Date objects
    return sessions.map((session: any) => ({
      ...session,
      lastActivity: new Date(session.lastActivity),
      messages: session.messages.map((msg: any) => ({
        ...msg,
        timestamp: new Date(msg.timestamp)
      }))
    }));
  } catch (error) {
    console.error('Failed to load chat sessions:', error);
    return [];
  }
}

export function getCurrentSession(): ChatSession | null {
  const sessions = getChatSessions();
  return sessions.length > 0 ? sessions[0] : null;
}

export function createNewSession(): ChatSession {
  const sessionId = `session_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;

  return {
    id: sessionId,
    title: 'New Neural Session',
    messages: [],
    lastActivity: new Date()
  };
}

export function generateSessionTitle(messages: Message[]): string {
  const firstUserMessage = messages.find(m => m.sender === 'user');
  if (firstUserMessage) {
    // Take first few words of the first user message as title
    const words = firstUserMessage.content.split(' ').slice(0, 4);
    return words.join(' ') + (firstUserMessage.content.split(' ').length > 4 ? '...' : '');
  }
  return 'Neural Session';
}

export async function importChatLogs(file: File): Promise<void> {
  const deviceId = getOrCreateDeviceId();
  const storageKey = `${CHAT_STORAGE_KEY}_${deviceId}`;

  try {
    const fileContent = await file.text();
    const importedSessions: ChatSession[] = JSON.parse(fileContent);

    const existingSessions = getChatSessions();
    const mergedSessions = [...importedSessions, ...existingSessions]
      .reduce((unique, session) => {
        if (!unique.some(s => s.id === session.id)) {
          unique.push(session);
        }
        return unique;
      }, [] as ChatSession[]);

    // Keep only the last 20 sessions to avoid storage bloat
    const limitedSessions = mergedSessions.slice(0, 20);

    localStorage.setItem(storageKey, JSON.stringify(limitedSessions));
  } catch (error) {
    console.error('Failed to import chat logs:', error);
  }
}

export function getRecursiveMemory(): Message[] {
  const sessions = getChatSessions();
  return sessions.flatMap(session => session.messages);
}
